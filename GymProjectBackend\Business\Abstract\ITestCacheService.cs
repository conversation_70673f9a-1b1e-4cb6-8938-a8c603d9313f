using Core.Utilities.Results;
using Entities.Concrete;

namespace Business.Abstract
{
    /// <summary>
    /// Cache Aspect testleri için test service interface
    /// </summary>
    public interface ITestCacheService
    {
        /// <summary>
        /// Cache aspect ile member getir (5 dakika cache)
        /// </summary>
        IDataResult<Member> GetMemberById(int memberId);

        /// <summary>
        /// Cache aspect ile member listesi getir (30 dakika cache)
        /// </summary>
        IDataResult<List<Member>> GetMemberList(int page, int size);

        /// <summary>
        /// Cache aspect ile member arama (15 dakika cache)
        /// </summary>
        IDataResult<List<Member>> SearchMembers(string searchTerm);

        /// <summary>
        /// Cache aspect ile istatistik getir (1 saat cache)
        /// </summary>
        IDataResult<object> GetMemberStatistics(string period);

        /// <summary>
        /// Member güncelle (cache invalidation trigger)
        /// </summary>
        IResult UpdateMember(Member member);

        /// <summary>
        /// Member sil (cache invalidation trigger)
        /// </summary>
        IResult DeleteMember(int memberId);

        /// <summary>
        /// Cache aspect olmadan member getir (performance karşılaştırması için)
        /// </summary>
        IDataResult<Member> GetMemberByIdWithoutCache(int memberId);

        /// <summary>
        /// Yavaş method (cache aspect performance testi için)
        /// </summary>
        IDataResult<object> SlowMethod(int delay);
    }
}
